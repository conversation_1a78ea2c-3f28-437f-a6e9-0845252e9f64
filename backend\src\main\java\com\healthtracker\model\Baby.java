package com.healthtracker.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;

/**
 * Baby entity representing child health information
 */
@Entity
@Table(name = "babies")
public class Baby {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "mother_id", nullable = false)
    private Mother mother;
    
    // Basic Information
    @NotBlank(message = "Baby name is required")
    @Column(nullable = false)
    private String name;
    
    @NotNull(message = "Date of birth is required")
    @Column(name = "date_of_birth", nullable = false)
    private LocalDate dateOfBirth;
    
    @Enumerated(EnumType.STRING)
    private Gender gender;
    
    @Column(name = "unique_id", unique = true)
    private String uniqueId;
    
    // Birth Information
    @Column(name = "birth_weight_kg")
    private Double birthWeightKg;
    
    @Column(name = "birth_length_cm")
    private Double birthLengthCm;
    
    @Column(name = "birth_head_circumference_cm")
    private Double birthHeadCircumferenceCm;
    
    @Column(name = "gestational_age_weeks")
    private Integer gestationalAgeWeeks;
    
    @Column(name = "birth_complications", columnDefinition = "TEXT")
    private String birthComplications;
    
    @Column(name = "delivery_type")
    private String deliveryType; // Natural, C-Section, etc.
    
    // Current Measurements
    @Column(name = "current_weight_kg")
    private Double currentWeightKg;
    
    @Column(name = "current_length_cm")
    private Double currentLengthCm;
    
    @Column(name = "current_head_circumference_cm")
    private Double currentHeadCircumferenceCm;
    
    // Medical Information
    @Column(name = "blood_type")
    private String bloodType;
    
    @Column(name = "allergies", columnDefinition = "TEXT")
    private String allergies;
    
    @Column(name = "medical_conditions", columnDefinition = "TEXT")
    private String medicalConditions;
    
    @Column(name = "medications", columnDefinition = "TEXT")
    private String medications;
    
    // Healthcare Provider
    @Column(name = "pediatrician_name")
    private String pediatricianName;
    
    @Column(name = "pediatrician_phone")
    private String pediatricianPhone;
    
    @Column(name = "hospital_of_birth")
    private String hospitalOfBirth;
    
    // Development Tracking
    @Column(name = "development_notes", columnDefinition = "TEXT")
    private String developmentNotes;
    
    @Column(name = "feeding_type")
    private String feedingType; // Breastfeeding, Formula, Mixed
    
    @Column(name = "sleep_pattern_notes", columnDefinition = "TEXT")
    private String sleepPatternNotes;
    
    // Profile
    @Column(name = "profile_picture_url")
    private String profilePictureUrl;
    
    @Column(name = "notes", columnDefinition = "TEXT")
    private String notes;
    
    // Timestamps
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    public enum Gender {
        MALE, FEMALE, OTHER
    }
    
    // Constructors
    public Baby() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.uniqueId = generateUniqueId();
    }
    
    public Baby(String name, LocalDate dateOfBirth, Gender gender, Mother mother) {
        this();
        this.name = name;
        this.dateOfBirth = dateOfBirth;
        this.gender = gender;
        this.mother = mother;
    }
    
    // Lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Business methods
    public int getAgeInMonths() {
        if (dateOfBirth == null) return 0;
        Period period = Period.between(dateOfBirth, LocalDate.now());
        return period.getYears() * 12 + period.getMonths();
    }
    
    public int getAgeInDays() {
        if (dateOfBirth == null) return 0;
        return (int) java.time.temporal.ChronoUnit.DAYS.between(dateOfBirth, LocalDate.now());
    }
    
    public String getAgeString() {
        if (dateOfBirth == null) return "Unknown";
        
        Period period = Period.between(dateOfBirth, LocalDate.now());
        int years = period.getYears();
        int months = period.getMonths();
        int days = period.getDays();
        
        if (years > 0) {
            return years + " year" + (years > 1 ? "s" : "") + 
                   (months > 0 ? " " + months + " month" + (months > 1 ? "s" : "") : "");
        } else if (months > 0) {
            return months + " month" + (months > 1 ? "s" : "") + 
                   (days > 0 ? " " + days + " day" + (days > 1 ? "s" : "") : "");
        } else {
            return days + " day" + (days > 1 ? "s" : "");
        }
    }
    
    private String generateUniqueId() {
        // Generate a unique ID for the baby (could be improved with better algorithm)
        return "BABY" + System.currentTimeMillis();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public Mother getMother() { return mother; }
    public void setMother(Mother mother) { this.mother = mother; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public LocalDate getDateOfBirth() { return dateOfBirth; }
    public void setDateOfBirth(LocalDate dateOfBirth) { this.dateOfBirth = dateOfBirth; }
    
    public Gender getGender() { return gender; }
    public void setGender(Gender gender) { this.gender = gender; }
    
    public String getUniqueId() { return uniqueId; }
    public void setUniqueId(String uniqueId) { this.uniqueId = uniqueId; }
    
    public Double getBirthWeightKg() { return birthWeightKg; }
    public void setBirthWeightKg(Double birthWeightKg) { this.birthWeightKg = birthWeightKg; }
    
    public Double getBirthLengthCm() { return birthLengthCm; }
    public void setBirthLengthCm(Double birthLengthCm) { this.birthLengthCm = birthLengthCm; }
    
    public Double getBirthHeadCircumferenceCm() { return birthHeadCircumferenceCm; }
    public void setBirthHeadCircumferenceCm(Double birthHeadCircumferenceCm) { this.birthHeadCircumferenceCm = birthHeadCircumferenceCm; }
    
    public Integer getGestationalAgeWeeks() { return gestationalAgeWeeks; }
    public void setGestationalAgeWeeks(Integer gestationalAgeWeeks) { this.gestationalAgeWeeks = gestationalAgeWeeks; }
    
    public String getBirthComplications() { return birthComplications; }
    public void setBirthComplications(String birthComplications) { this.birthComplications = birthComplications; }
    
    public String getDeliveryType() { return deliveryType; }
    public void setDeliveryType(String deliveryType) { this.deliveryType = deliveryType; }
    
    public Double getCurrentWeightKg() { return currentWeightKg; }
    public void setCurrentWeightKg(Double currentWeightKg) { this.currentWeightKg = currentWeightKg; }
    
    public Double getCurrentLengthCm() { return currentLengthCm; }
    public void setCurrentLengthCm(Double currentLengthCm) { this.currentLengthCm = currentLengthCm; }
    
    public Double getCurrentHeadCircumferenceCm() { return currentHeadCircumferenceCm; }
    public void setCurrentHeadCircumferenceCm(Double currentHeadCircumferenceCm) { this.currentHeadCircumferenceCm = currentHeadCircumferenceCm; }
    
    public String getBloodType() { return bloodType; }
    public void setBloodType(String bloodType) { this.bloodType = bloodType; }
    
    public String getAllergies() { return allergies; }
    public void setAllergies(String allergies) { this.allergies = allergies; }
    
    public String getMedicalConditions() { return medicalConditions; }
    public void setMedicalConditions(String medicalConditions) { this.medicalConditions = medicalConditions; }
    
    public String getMedications() { return medications; }
    public void setMedications(String medications) { this.medications = medications; }
    
    public String getPediatricianName() { return pediatricianName; }
    public void setPediatricianName(String pediatricianName) { this.pediatricianName = pediatricianName; }
    
    public String getPediatricianPhone() { return pediatricianPhone; }
    public void setPediatricianPhone(String pediatricianPhone) { this.pediatricianPhone = pediatricianPhone; }
    
    public String getHospitalOfBirth() { return hospitalOfBirth; }
    public void setHospitalOfBirth(String hospitalOfBirth) { this.hospitalOfBirth = hospitalOfBirth; }
    
    public String getDevelopmentNotes() { return developmentNotes; }
    public void setDevelopmentNotes(String developmentNotes) { this.developmentNotes = developmentNotes; }
    
    public String getFeedingType() { return feedingType; }
    public void setFeedingType(String feedingType) { this.feedingType = feedingType; }
    
    public String getSleepPatternNotes() { return sleepPatternNotes; }
    public void setSleepPatternNotes(String sleepPatternNotes) { this.sleepPatternNotes = sleepPatternNotes; }
    
    public String getProfilePictureUrl() { return profilePictureUrl; }
    public void setProfilePictureUrl(String profilePictureUrl) { this.profilePictureUrl = profilePictureUrl; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
