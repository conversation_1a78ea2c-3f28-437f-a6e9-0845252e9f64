# Supabase Authentication Setup

This document outlines the authentication configuration for the Maternal & Child Health Tracking System.

## Authentication Configuration

### 1. Enable Authentication Providers

In your Supabase dashboard, navigate to Authentication > Settings and enable:

- **Email/Password**: Primary authentication method
- **Google OAuth** (optional): For social login
- **Apple OAuth** (optional): For iOS users

### 2. Email Templates

Configure custom email templates in Authentication > Templates:

#### Confirm Signup Template
```html
<h2>Welcome to MaBa Health!</h2>
<p>Thank you for joining our maternal and child health community.</p>
<p>Please confirm your email address by clicking the link below:</p>
<p><a href="{{ .ConfirmationURL }}">Confirm Email Address</a></p>
<p>This link will expire in 24 hours.</p>
<p>Best regards,<br>The MaBa Health Team</p>
```

#### Reset Password Template
```html
<h2>Reset Your Password</h2>
<p>We received a request to reset your password for your MaBa Health account.</p>
<p>Click the link below to reset your password:</p>
<p><a href="{{ .ConfirmationURL }}">Reset Password</a></p>
<p>If you didn't request this, please ignore this email.</p>
<p>This link will expire in 1 hour.</p>
<p>Best regards,<br>The MaBa Health Team</p>
```

### 3. URL Configuration

Set the following URLs in Authentication > Settings:

- **Site URL**: `http://localhost:3000` (development) / `https://yourdomain.com` (production)
- **Redirect URLs**: 
  - `http://localhost:3000/auth/callback`
  - `https://yourdomain.com/auth/callback`

### 4. Security Settings

Configure the following security settings:

- **Enable email confirmations**: Yes
- **Enable secure password change**: Yes
- **Password minimum length**: 8 characters
- **JWT expiry**: 3600 seconds (1 hour)
- **Refresh token rotation**: Enabled

### 5. Row Level Security (RLS) Policies

Apply the following RLS policies to secure your data:

#### Users Table
```sql
-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Users can read their own data
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid()::text = id::text);

-- Users can update their own data
CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid()::text = id::text);
```

#### Mothers Table
```sql
-- Enable RLS
ALTER TABLE mothers ENABLE ROW LEVEL SECURITY;

-- Mothers can access their own data
CREATE POLICY "Mothers can view own data" ON mothers
    FOR ALL USING (
        user_id IN (
            SELECT id FROM users WHERE auth.uid()::text = id::text
        )
    );

-- Doctors can view their patients' data
CREATE POLICY "Doctors can view patient data" ON mothers
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE auth.uid()::text = id::text 
            AND role = 'DOCTOR'
        )
    );
```

#### Babies Table
```sql
-- Enable RLS
ALTER TABLE babies ENABLE ROW LEVEL SECURITY;

-- Mothers can access their babies' data
CREATE POLICY "Mothers can view own babies" ON babies
    FOR ALL USING (
        mother_id IN (
            SELECT id FROM mothers 
            WHERE user_id IN (
                SELECT id FROM users WHERE auth.uid()::text = id::text
            )
        )
    );

-- Doctors can view their patients' babies
CREATE POLICY "Doctors can view patient babies" ON babies
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE auth.uid()::text = id::text 
            AND role = 'DOCTOR'
        )
    );
```

#### Health Records Table
```sql
-- Enable RLS
ALTER TABLE health_records ENABLE ROW LEVEL SECURITY;

-- Users can access their own health records
CREATE POLICY "Users can view own health records" ON health_records
    FOR ALL USING (
        user_id IN (
            SELECT id FROM users WHERE auth.uid()::text = id::text
        )
    );

-- Doctors can view their patients' health records
CREATE POLICY "Doctors can view patient health records" ON health_records
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE auth.uid()::text = id::text 
            AND role = 'DOCTOR'
        )
    );
```

### 6. Custom Claims

Set up custom claims for role-based access:

```sql
-- Function to get user role
CREATE OR REPLACE FUNCTION get_user_role(user_id uuid)
RETURNS text AS $$
BEGIN
    RETURN (
        SELECT role::text 
        FROM users 
        WHERE id = user_id::text::bigint
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is doctor
CREATE OR REPLACE FUNCTION is_doctor(user_id uuid)
RETURNS boolean AS $$
BEGIN
    RETURN (
        SELECT role = 'DOCTOR' 
        FROM users 
        WHERE id = user_id::text::bigint
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(user_id uuid)
RETURNS boolean AS $$
BEGIN
    RETURN (
        SELECT role = 'ADMIN' 
        FROM users 
        WHERE id = user_id::text::bigint
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 7. Database Functions for Authentication

```sql
-- Function to create user profile after signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
    INSERT INTO users (id, email, name, role, email_verified, created_at)
    VALUES (
        NEW.id::text::bigint,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'role', 'USER')::user_role,
        NEW.email_confirmed_at IS NOT NULL,
        NEW.created_at
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();
```

### 8. Environment Variables

Set the following environment variables in your application:

```env
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key
JWT_SECRET=your-jwt-secret
```

### 9. Testing Authentication

Use the following test accounts for development:

#### Test Mother Account
- Email: `<EMAIL>`
- Password: `TestPassword123!`
- Role: `USER`

#### Test Doctor Account
- Email: `<EMAIL>`
- Password: `TestPassword123!`
- Role: `DOCTOR`

#### Test Admin Account
- Email: `<EMAIL>`
- Password: `TestPassword123!`
- Role: `ADMIN`

### 10. Production Considerations

Before going to production:

1. **Update Site URL** to your production domain
2. **Configure custom SMTP** for email delivery
3. **Set up proper SSL certificates**
4. **Review and test all RLS policies**
5. **Enable audit logging**
6. **Set up monitoring and alerts**
7. **Configure backup and recovery**

### 11. API Integration

The authentication system integrates with your Spring Boot backend through:

1. **JWT token validation**
2. **Role-based access control**
3. **User session management**
4. **Automatic user profile creation**

Refer to the backend security configuration for implementation details.
