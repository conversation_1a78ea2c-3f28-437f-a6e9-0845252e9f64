<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - MaBa Health</title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .auth-container {
            max-width: 400px;
            margin: 2rem auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .auth-tabs {
            display: flex;
            margin-bottom: 2rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .auth-tab {
            flex: 1;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .auth-tab.active {
            border-bottom-color: #667eea;
            color: #667eea;
            font-weight: 600;
        }
        
        .auth-form {
            display: none;
        }
        
        .auth-form.active {
            display: block;
        }
        
        .role-selector {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .role-option {
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .role-option:hover {
            border-color: #667eea;
        }
        
        .role-option.selected {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .role-option input[type="radio"] {
            display: none;
        }
        
        .field-error {
            color: #e74c3c;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        
        .form-control.error {
            border-color: #e74c3c;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav container">
            <div class="logo">
                <img src="../assets/images/logo.png" alt="Health Tracker Logo" onerror="this.style.display='none'">
                <span>MaBa Health</span>
            </div>
            <ul class="nav-links">
                <li><a href="index.html">← Back to Home</a></li>
            </ul>
        </nav>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="auth-container">
                <div class="text-center mb-2">
                    <h1>Welcome to MaBa Health</h1>
                    <p>Your comprehensive maternal and child health companion</p>
                </div>

                <div class="auth-tabs">
                    <div class="auth-tab active" data-tab="login">Login</div>
                    <div class="auth-tab" data-tab="register">Sign Up</div>
                </div>

                <!-- Login Form -->
                <form id="loginForm" class="auth-form active">
                    <div class="form-group">
                        <label for="loginEmail">Email Address</label>
                        <input type="email" id="loginEmail" name="email" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="loginPassword">Password</label>
                        <input type="password" id="loginPassword" name="password" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label>Login as:</label>
                        <div class="role-selector">
                            <label class="role-option selected">
                                <input type="radio" name="role" value="user" checked>
                                <div>👩‍👶</div>
                                <div>Mother</div>
                            </label>
                            <label class="role-option">
                                <input type="radio" name="role" value="doctor">
                                <div>👨‍⚕️</div>
                                <div>Doctor</div>
                            </label>
                            <label class="role-option">
                                <input type="radio" name="role" value="admin">
                                <div>⚙️</div>
                                <div>Admin</div>
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn" style="width: 100%;">Login</button>
                    
                    <div class="text-center mt-1">
                        <a href="#" style="color: #667eea;">Forgot Password?</a>
                    </div>
                </form>

                <!-- Register Form -->
                <form id="registerForm" class="auth-form">
                    <div class="form-group">
                        <label for="registerName">Full Name</label>
                        <input type="text" id="registerName" name="name" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="registerEmail">Email Address</label>
                        <input type="email" id="registerEmail" name="email" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="registerPhone">Phone Number</label>
                        <input type="tel" id="registerPhone" name="phone" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="registerPassword">Password</label>
                        <input type="password" id="registerPassword" name="password" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword">Confirm Password</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" class="form-control" required>
                    </div>

                    <div class="form-group">
                        <label>Register as:</label>
                        <div class="role-selector">
                            <label class="role-option selected">
                                <input type="radio" name="role" value="user" checked>
                                <div>👩‍👶</div>
                                <div>Mother</div>
                            </label>
                            <label class="role-option">
                                <input type="radio" name="role" value="doctor">
                                <div>👨‍⚕️</div>
                                <div>Doctor</div>
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn" style="width: 100%;">Create Account</button>
                </form>
            </div>
        </div>
    </main>

    <script src="../js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab switching
            const tabs = document.querySelectorAll('.auth-tab');
            const forms = document.querySelectorAll('.auth-form');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const targetTab = tab.dataset.tab;
                    
                    // Update active tab
                    tabs.forEach(t => t.classList.remove('active'));
                    tab.classList.add('active');
                    
                    // Update active form
                    forms.forEach(f => f.classList.remove('active'));
                    document.getElementById(targetTab + 'Form').classList.add('active');
                });
            });

            // Role selection
            document.querySelectorAll('.role-selector').forEach(selector => {
                selector.addEventListener('click', (e) => {
                    if (e.target.closest('.role-option')) {
                        const roleOption = e.target.closest('.role-option');
                        const radio = roleOption.querySelector('input[type="radio"]');
                        
                        // Update selection
                        selector.querySelectorAll('.role-option').forEach(opt => opt.classList.remove('selected'));
                        roleOption.classList.add('selected');
                        radio.checked = true;
                    }
                });
            });

            // Check URL parameters for pre-selection
            const urlParams = new URLSearchParams(window.location.search);
            const type = urlParams.get('type');
            if (type) {
                const roleMapping = {
                    'mother': 'user',
                    'child': 'user',
                    'doctor': 'doctor'
                };
                
                const role = roleMapping[type] || 'user';
                document.querySelectorAll(`input[name="role"][value="${role}"]`).forEach(radio => {
                    radio.checked = true;
                    radio.closest('.role-option').classList.add('selected');
                    radio.closest('.role-selector').querySelectorAll('.role-option').forEach(opt => {
                        if (opt !== radio.closest('.role-option')) {
                            opt.classList.remove('selected');
                        }
                    });
                });
            }

            // Form submissions
            FormHandler.handleSubmit('loginForm', async (data) => {
                const form = document.getElementById('loginForm');
                form.classList.add('loading');
                
                const result = await authManager.login(data.email, data.password, data.role);
                
                if (!result.success) {
                    Utils.showNotification(result.message, 'error');
                }
                
                form.classList.remove('loading');
            });

            FormHandler.handleSubmit('registerForm', async (data) => {
                const form = document.getElementById('registerForm');
                
                // Validate passwords match
                if (data.password !== data.confirmPassword) {
                    Utils.showNotification('Passwords do not match', 'error');
                    return;
                }
                
                // Validate form
                const isValid = FormHandler.validateForm('registerForm', {
                    name: { required: true, label: 'Full Name' },
                    email: { required: true, type: 'email', label: 'Email' },
                    phone: { required: true, type: 'phone', label: 'Phone' },
                    password: { required: true, label: 'Password' }
                });
                
                if (!isValid) return;
                
                form.classList.add('loading');
                
                const result = await authManager.register(data);
                
                if (result.success) {
                    // Switch to login tab
                    document.querySelector('.auth-tab[data-tab="login"]').click();
                }
                
                form.classList.remove('loading');
            });
        });
    </script>
</body>
</html>
