<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Health Tracker - MaBa Health</title>
    <link rel="stylesheet" href="../../css/style.css">
    <style>
        .metric-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }
        
        .metric-input {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .metric-input label {
            min-width: 120px;
            font-weight: 600;
        }
        
        .metric-input input {
            flex: 1;
            max-width: 200px;
        }
        
        .metric-history {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #f0f0f0;
        }
        
        .history-item {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f8f9fa;
        }
        
        .chart-container {
            height: 300px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            margin-top: 1rem;
        }
        
        .alert-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .alert-box.success {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        
        .alert-box.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        
        .alert-box.danger {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav container">
            <div class="logo">
                <img src="../../assets/images/logo.png" alt="Health Tracker Logo" onerror="this.style.display='none'">
                <span>MaBa Health</span>
            </div>
            <ul class="nav-links">
                <li><a href="../dashboard-user.html">← Back to Dashboard</a></li>
                <li><a href="#" class="logout-btn">Logout</a></li>
            </ul>
        </nav>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="dashboard">
                <div class="dashboard-header">
                    <h1>Health Tracker</h1>
                    <p>Monitor your daily health metrics and track your progress</p>
                </div>

                <div id="healthAlerts"></div>

                <div class="grid">
                    <div class="metric-card">
                        <h3>📊 Vital Signs</h3>
                        <form id="vitalsForm">
                            <div class="metric-input">
                                <label>Blood Pressure:</label>
                                <input type="text" name="bloodPressure" placeholder="120/80" class="form-control">
                                <span>mmHg</span>
                            </div>
                            <div class="metric-input">
                                <label>Heart Rate:</label>
                                <input type="number" name="heartRate" placeholder="72" class="form-control">
                                <span>bpm</span>
                            </div>
                            <div class="metric-input">
                                <label>Temperature:</label>
                                <input type="number" name="temperature" step="0.1" placeholder="98.6" class="form-control">
                                <span>°F</span>
                            </div>
                            <button type="submit" class="btn">Record Vitals</button>
                        </form>
                        
                        <div class="metric-history">
                            <h4>Recent Readings</h4>
                            <div id="vitalsHistory">
                                <div class="history-item">
                                    <span>Today 9:00 AM</span>
                                    <span>BP: 118/76, HR: 74, Temp: 98.4°F</span>
                                </div>
                                <div class="history-item">
                                    <span>Yesterday 8:30 AM</span>
                                    <span>BP: 120/78, HR: 72, Temp: 98.6°F</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="metric-card">
                        <h3>⚖️ Weight Tracking</h3>
                        <form id="weightForm">
                            <div class="metric-input">
                                <label>Current Weight:</label>
                                <input type="number" name="weight" step="0.1" placeholder="150.5" class="form-control">
                                <span>lbs</span>
                            </div>
                            <button type="submit" class="btn">Record Weight</button>
                        </form>
                        
                        <div class="chart-container">
                            Weight Trend Chart
                            <br><small>(Chart visualization would be implemented here)</small>
                        </div>
                        
                        <div class="metric-history">
                            <h4>Weight History</h4>
                            <div id="weightHistory">
                                <div class="history-item">
                                    <span>Today</span>
                                    <span>152.3 lbs</span>
                                </div>
                                <div class="history-item">
                                    <span>1 week ago</span>
                                    <span>151.8 lbs</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid">
                    <div class="metric-card">
                        <h3>😴 Sleep Tracking</h3>
                        <form id="sleepForm">
                            <div class="metric-input">
                                <label>Bedtime:</label>
                                <input type="time" name="bedtime" class="form-control">
                            </div>
                            <div class="metric-input">
                                <label>Wake Time:</label>
                                <input type="time" name="wakeTime" class="form-control">
                            </div>
                            <div class="metric-input">
                                <label>Sleep Quality:</label>
                                <select name="sleepQuality" class="form-control">
                                    <option value="">Select quality</option>
                                    <option value="excellent">Excellent</option>
                                    <option value="good">Good</option>
                                    <option value="fair">Fair</option>
                                    <option value="poor">Poor</option>
                                </select>
                            </div>
                            <button type="submit" class="btn">Record Sleep</button>
                        </form>
                    </div>

                    <div class="metric-card">
                        <h3>💧 Hydration</h3>
                        <form id="hydrationForm">
                            <div class="metric-input">
                                <label>Water Intake:</label>
                                <input type="number" name="waterIntake" placeholder="8" class="form-control">
                                <span>glasses</span>
                            </div>
                            <button type="submit" class="btn">Record Intake</button>
                        </form>
                        
                        <div class="mt-1">
                            <div style="background: #e3f2fd; padding: 1rem; border-radius: 8px;">
                                <strong>Today's Goal: 8 glasses</strong>
                                <div style="background: #fff; height: 20px; border-radius: 10px; margin-top: 0.5rem; overflow: hidden;">
                                    <div id="hydrationProgress" style="background: #2196f3; height: 100%; width: 60%; transition: width 0.3s ease;"></div>
                                </div>
                                <small>5 of 8 glasses completed</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="metric-card">
                    <h3>📝 Symptoms & Notes</h3>
                    <form id="symptomsForm">
                        <div class="form-group">
                            <label>Symptoms (if any):</label>
                            <div style="display: flex; flex-wrap: wrap; gap: 0.5rem; margin-bottom: 1rem;">
                                <label style="display: flex; align-items: center; gap: 0.25rem;">
                                    <input type="checkbox" name="symptoms" value="nausea"> Nausea
                                </label>
                                <label style="display: flex; align-items: center; gap: 0.25rem;">
                                    <input type="checkbox" name="symptoms" value="fatigue"> Fatigue
                                </label>
                                <label style="display: flex; align-items: center; gap: 0.25rem;">
                                    <input type="checkbox" name="symptoms" value="headache"> Headache
                                </label>
                                <label style="display: flex; align-items: center; gap: 0.25rem;">
                                    <input type="checkbox" name="symptoms" value="backache"> Back Pain
                                </label>
                                <label style="display: flex; align-items: center; gap: 0.25rem;">
                                    <input type="checkbox" name="symptoms" value="swelling"> Swelling
                                </label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Additional Notes:</label>
                            <textarea name="notes" class="form-control" rows="3" placeholder="Any additional observations or concerns..."></textarea>
                        </div>
                        
                        <button type="submit" class="btn">Save Entry</button>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script src="../../js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!authManager.isAuthenticated()) {
                window.location.href = '../login.html';
                return;
            }

            // Initialize forms
            initializeForms();
            loadHealthData();
            checkHealthAlerts();
        });

        function initializeForms() {
            // Vitals form
            FormHandler.handleSubmit('vitalsForm', async (data) => {
                const result = await apiManager.post('/user/health/vitals', {
                    ...data,
                    timestamp: new Date().toISOString()
                });
                
                if (result) {
                    Utils.showNotification('Vitals recorded successfully!', 'success');
                    loadHealthData();
                    checkHealthAlerts();
                }
            });

            // Weight form
            FormHandler.handleSubmit('weightForm', async (data) => {
                const result = await apiManager.post('/user/health/weight', {
                    ...data,
                    timestamp: new Date().toISOString()
                });
                
                if (result) {
                    Utils.showNotification('Weight recorded successfully!', 'success');
                    loadHealthData();
                }
            });

            // Sleep form
            FormHandler.handleSubmit('sleepForm', async (data) => {
                const result = await apiManager.post('/user/health/sleep', {
                    ...data,
                    date: new Date().toISOString().split('T')[0]
                });
                
                if (result) {
                    Utils.showNotification('Sleep data recorded successfully!', 'success');
                }
            });

            // Hydration form
            FormHandler.handleSubmit('hydrationForm', async (data) => {
                const result = await apiManager.post('/user/health/hydration', {
                    ...data,
                    timestamp: new Date().toISOString()
                });
                
                if (result) {
                    Utils.showNotification('Hydration recorded successfully!', 'success');
                    updateHydrationProgress();
                }
            });

            // Symptoms form
            FormHandler.handleSubmit('symptomsForm', async (data) => {
                const symptoms = Array.from(document.querySelectorAll('input[name="symptoms"]:checked'))
                    .map(cb => cb.value);
                
                const result = await apiManager.post('/user/health/symptoms', {
                    symptoms,
                    notes: data.notes,
                    timestamp: new Date().toISOString()
                });
                
                if (result) {
                    Utils.showNotification('Symptoms recorded successfully!', 'success');
                    document.getElementById('symptomsForm').reset();
                }
            });
        }

        async function loadHealthData() {
            try {
                // Load recent vitals
                const vitals = await apiManager.get('/user/health/vitals/recent');
                if (vitals && vitals.length > 0) {
                    updateVitalsHistory(vitals);
                }

                // Load weight history
                const weights = await apiManager.get('/user/health/weight/recent');
                if (weights && weights.length > 0) {
                    updateWeightHistory(weights);
                }
            } catch (error) {
                console.error('Error loading health data:', error);
            }
        }

        function updateVitalsHistory(vitals) {
            const historyContainer = document.getElementById('vitalsHistory');
            historyContainer.innerHTML = vitals.map(vital => `
                <div class="history-item">
                    <span>${Utils.formatDate(vital.timestamp)}</span>
                    <span>BP: ${vital.bloodPressure}, HR: ${vital.heartRate}, Temp: ${vital.temperature}°F</span>
                </div>
            `).join('');
        }

        function updateWeightHistory(weights) {
            const historyContainer = document.getElementById('weightHistory');
            historyContainer.innerHTML = weights.map(weight => `
                <div class="history-item">
                    <span>${Utils.formatDate(weight.timestamp)}</span>
                    <span>${weight.weight} lbs</span>
                </div>
            `).join('');
        }

        async function checkHealthAlerts() {
            try {
                const alerts = await apiManager.get('/user/health/alerts');
                const alertsContainer = document.getElementById('healthAlerts');
                
                if (alerts && alerts.length > 0) {
                    alertsContainer.innerHTML = alerts.map(alert => `
                        <div class="alert-box ${alert.type}">
                            <strong>${alert.title}</strong>
                            <p>${alert.message}</p>
                        </div>
                    `).join('');
                } else {
                    alertsContainer.innerHTML = `
                        <div class="alert-box success">
                            <strong>All Good!</strong>
                            <p>Your health metrics are within normal ranges.</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Error checking health alerts:', error);
            }
        }

        function updateHydrationProgress() {
            // This would be updated based on actual data
            // For demo purposes, showing static progress
        }
    </script>
</body>
</html>
