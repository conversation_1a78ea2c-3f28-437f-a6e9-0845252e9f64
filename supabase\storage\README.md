# Supabase Storage Configuration

This document outlines the storage setup for the Maternal & Child Health Tracking System.

## Storage Buckets

### 1. Profile Pictures Bucket

**Bucket Name**: `profile-pictures`

**Configuration**:
- **Public**: Yes
- **File size limit**: 5MB
- **Allowed file types**: `image/jpeg`, `image/png`, `image/webp`

**RLS Policies**:
```sql
-- Allow users to upload their own profile pictures
CREATE POLICY "Users can upload own profile picture" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'profile-pictures' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Allow users to view their own profile pictures
CREATE POLICY "Users can view own profile picture" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'profile-pictures' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Allow users to update their own profile pictures
CREATE POLICY "Users can update own profile picture" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'profile-pictures' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Allow users to delete their own profile pictures
CREATE POLICY "Users can delete own profile picture" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'profile-pictures' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );
```

**Folder Structure**:
```
profile-pictures/
├── {user_id}/
│   ├── avatar.jpg
│   └── thumbnail.jpg
```

### 2. Baby Photos Bucket

**Bucket Name**: `baby-photos`

**Configuration**:
- **Public**: No (private with signed URLs)
- **File size limit**: 10MB
- **Allowed file types**: `image/jpeg`, `image/png`, `image/webp`

**RLS Policies**:
```sql
-- Allow mothers to upload photos of their babies
CREATE POLICY "Mothers can upload baby photos" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'baby-photos' AND
        EXISTS (
            SELECT 1 FROM babies b
            JOIN mothers m ON b.mother_id = m.id
            JOIN users u ON m.user_id = u.id
            WHERE auth.uid()::text = u.id::text
            AND b.id::text = (storage.foldername(name))[2]
        )
    );

-- Allow mothers to view photos of their babies
CREATE POLICY "Mothers can view baby photos" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'baby-photos' AND
        EXISTS (
            SELECT 1 FROM babies b
            JOIN mothers m ON b.mother_id = m.id
            JOIN users u ON m.user_id = u.id
            WHERE auth.uid()::text = u.id::text
            AND b.id::text = (storage.foldername(name))[2]
        )
    );

-- Allow doctors to view baby photos of their patients
CREATE POLICY "Doctors can view patient baby photos" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'baby-photos' AND
        EXISTS (
            SELECT 1 FROM users 
            WHERE auth.uid()::text = id::text 
            AND role = 'DOCTOR'
        )
    );
```

**Folder Structure**:
```
baby-photos/
├── {mother_id}/
│   ├── {baby_id}/
│   │   ├── profile.jpg
│   │   ├── milestones/
│   │   │   ├── first_smile.jpg
│   │   │   └── first_steps.jpg
│   │   └── growth/
│   │       ├── month_1.jpg
│   │       └── month_2.jpg
```

### 3. Health Documents Bucket

**Bucket Name**: `health-documents`

**Configuration**:
- **Public**: No (private with signed URLs)
- **File size limit**: 20MB
- **Allowed file types**: `application/pdf`, `image/jpeg`, `image/png`

**RLS Policies**:
```sql
-- Allow users to upload their health documents
CREATE POLICY "Users can upload health documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'health-documents' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Allow users to view their health documents
CREATE POLICY "Users can view health documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'health-documents' AND
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- Allow doctors to view patient health documents
CREATE POLICY "Doctors can view patient health documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'health-documents' AND
        EXISTS (
            SELECT 1 FROM users 
            WHERE auth.uid()::text = id::text 
            AND role = 'DOCTOR'
        )
    );
```

**Folder Structure**:
```
health-documents/
├── {user_id}/
│   ├── lab-results/
│   │   ├── blood_test_2024_01.pdf
│   │   └── ultrasound_2024_02.pdf
│   ├── prescriptions/
│   │   └── prenatal_vitamins.pdf
│   └── reports/
│       └── checkup_summary.pdf
```

### 4. Vaccination Records Bucket

**Bucket Name**: `vaccination-records`

**Configuration**:
- **Public**: No (private with signed URLs)
- **File size limit**: 10MB
- **Allowed file types**: `application/pdf`, `image/jpeg`, `image/png`

**RLS Policies**:
```sql
-- Allow mothers to upload vaccination records for their babies
CREATE POLICY "Mothers can upload vaccination records" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'vaccination-records' AND
        EXISTS (
            SELECT 1 FROM babies b
            JOIN mothers m ON b.mother_id = m.id
            JOIN users u ON m.user_id = u.id
            WHERE auth.uid()::text = u.id::text
            AND b.id::text = (storage.foldername(name))[2]
        )
    );

-- Allow mothers to view vaccination records of their babies
CREATE POLICY "Mothers can view vaccination records" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'vaccination-records' AND
        EXISTS (
            SELECT 1 FROM babies b
            JOIN mothers m ON b.mother_id = m.id
            JOIN users u ON m.user_id = u.id
            WHERE auth.uid()::text = u.id::text
            AND b.id::text = (storage.foldername(name))[2]
        )
    );
```

**Folder Structure**:
```
vaccination-records/
├── {mother_id}/
│   ├── {baby_id}/
│   │   ├── vaccination_card.pdf
│   │   ├── hepatitis_b.pdf
│   │   └── dtap_series.pdf
```

## Storage Functions

### 1. Generate Signed URL Function

```sql
CREATE OR REPLACE FUNCTION generate_signed_url(
    bucket_name text,
    file_path text,
    expires_in integer DEFAULT 3600
)
RETURNS text AS $$
DECLARE
    signed_url text;
BEGIN
    SELECT storage.sign(bucket_name, file_path, expires_in) INTO signed_url;
    RETURN signed_url;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 2. File Upload Validation Function

```sql
CREATE OR REPLACE FUNCTION validate_file_upload(
    bucket_name text,
    file_name text,
    file_size bigint,
    mime_type text
)
RETURNS boolean AS $$
BEGIN
    -- Check file size limits
    IF bucket_name = 'profile-pictures' AND file_size > 5242880 THEN -- 5MB
        RETURN false;
    END IF;
    
    IF bucket_name = 'baby-photos' AND file_size > 10485760 THEN -- 10MB
        RETURN false;
    END IF;
    
    IF bucket_name = 'health-documents' AND file_size > 20971520 THEN -- 20MB
        RETURN false;
    END IF;
    
    -- Check mime types
    IF bucket_name IN ('profile-pictures', 'baby-photos') THEN
        IF mime_type NOT IN ('image/jpeg', 'image/png', 'image/webp') THEN
            RETURN false;
        END IF;
    END IF;
    
    IF bucket_name = 'health-documents' THEN
        IF mime_type NOT IN ('application/pdf', 'image/jpeg', 'image/png') THEN
            RETURN false;
        END IF;
    END IF;
    
    RETURN true;
END;
$$ LANGUAGE plpgsql;
```

## Usage Examples

### Frontend JavaScript

```javascript
// Upload profile picture
async function uploadProfilePicture(file) {
    const fileExt = file.name.split('.').pop();
    const fileName = `${user.id}/avatar.${fileExt}`;
    
    const { data, error } = await supabase.storage
        .from('profile-pictures')
        .upload(fileName, file, {
            cacheControl: '3600',
            upsert: true
        });
    
    if (error) throw error;
    return data;
}

// Get signed URL for private file
async function getSignedUrl(bucket, path) {
    const { data, error } = await supabase.storage
        .from(bucket)
        .createSignedUrl(path, 3600); // 1 hour expiry
    
    if (error) throw error;
    return data.signedUrl;
}
```

### Backend Java

```java
// Upload file using Supabase client
public String uploadFile(String bucket, String path, byte[] fileData) {
    // Implementation using Supabase Java client
    return supabaseClient.storage()
        .from(bucket)
        .upload(path, fileData);
}
```

## Security Considerations

1. **File Type Validation**: Always validate file types on both client and server
2. **File Size Limits**: Enforce appropriate size limits for each bucket
3. **Virus Scanning**: Consider implementing virus scanning for uploaded files
4. **Content Moderation**: Implement content moderation for user-uploaded images
5. **Backup Strategy**: Set up regular backups of critical storage buckets
6. **Access Logging**: Enable access logging for audit purposes

## Monitoring and Maintenance

1. **Storage Usage**: Monitor storage usage and set up alerts
2. **Performance**: Monitor upload/download performance
3. **Error Tracking**: Track and analyze upload/download errors
4. **Cleanup**: Implement cleanup policies for orphaned files
5. **CDN Integration**: Consider CDN integration for better performance
