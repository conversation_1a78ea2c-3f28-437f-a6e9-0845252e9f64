package com.healthtracker;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Main application class for the Maternal & Child Health Tracking System
 * 
 * This application provides comprehensive health monitoring and tracking
 * capabilities for mothers and children, with AI-powered insights and
 * healthcare provider integration.
 * 
 * Features:
 * - Maternal health tracking (pregnancy timeline, vitals, appointments)
 * - Child health monitoring (vaccinations, growth, milestones)
 * - Healthcare provider dashboard
 * - AI-powered health alerts and recommendations
 * - Secure authentication and role-based access control
 * 
 * <AUTHOR> Health Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableAsync
@EnableScheduling
public class MaternalChildHealthApplication {

    public static void main(String[] args) {
        SpringApplication.run(MaternalChildHealthApplication.class, args);
        System.out.println("🏥 Maternal & Child Health Tracking System Started Successfully!");
        System.out.println("📊 Dashboard: http://localhost:8080");
        System.out.println("📖 API Documentation: http://localhost:8080/swagger-ui.html");
        System.out.println("🔍 Health Check: http://localhost:8080/actuator/health");
    }
}
