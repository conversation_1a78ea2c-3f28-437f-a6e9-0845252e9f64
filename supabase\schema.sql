-- Maternal & Child Health Tracking System Database Schema
-- Supabase PostgreSQL Database

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types
CREATE TYPE user_role AS ENUM ('USER', 'DOCTOR', 'ADMIN');
CREATE TYPE baby_gender AS ENUM ('MALE', 'FEMALE', 'OTHER');
CREATE TYPE appointment_status AS ENUM ('SCHEDULED', 'CONFIRMED', 'COMPLETED', 'CANCELLED', 'RESCHEDULED');
CREATE TYPE vaccination_status AS ENUM ('SCHEDULED', 'COMPLETED', 'OVERDUE', 'SKIPPED');
CREATE TYPE health_record_type AS ENUM ('VITALS', 'WEIGHT', 'SLEEP', 'SYMPTOMS', 'MEDICATION', 'APPOINTMENT', 'LAB_RESULT');

-- Users table (handles authentication and basic user info)
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    name VARCHA<PERSON>(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone_number VARCHAR(20),
    password VARCHAR(255) NOT NULL,
    role user_role NOT NULL DEFAULT 'USER',
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    date_of_birth TIMESTAMP,
    profile_picture_url TEXT,
    bio TEXT,
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    emergency_contact_relationship VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    last_login TIMESTAMP
);

-- Mothers table (maternal health information)
CREATE TABLE mothers (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    is_pregnant BOOLEAN DEFAULT FALSE,
    due_date DATE,
    last_menstrual_period DATE,
    pregnancy_week INTEGER,
    pregnancy_trimester INTEGER,
    number_of_pregnancies INTEGER DEFAULT 0,
    number_of_births INTEGER DEFAULT 0,
    number_of_miscarriages INTEGER DEFAULT 0,
    blood_type VARCHAR(10),
    height_cm DECIMAL(5,2),
    pre_pregnancy_weight_kg DECIMAL(5,2),
    current_weight_kg DECIMAL(5,2),
    bmi DECIMAL(4,2),
    medical_conditions TEXT,
    allergies TEXT,
    medications TEXT,
    family_medical_history TEXT,
    primary_doctor_name VARCHAR(100),
    primary_doctor_phone VARCHAR(20),
    hospital_preference VARCHAR(100),
    insurance_provider VARCHAR(100),
    insurance_policy_number VARCHAR(50),
    preferred_language VARCHAR(20) DEFAULT 'English',
    notification_preferences TEXT,
    privacy_settings TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Babies table (child health information)
CREATE TABLE babies (
    id BIGSERIAL PRIMARY KEY,
    mother_id BIGINT REFERENCES mothers(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    gender baby_gender,
    unique_id VARCHAR(50) UNIQUE,
    birth_weight_kg DECIMAL(4,3),
    birth_length_cm DECIMAL(4,1),
    birth_head_circumference_cm DECIMAL(4,1),
    gestational_age_weeks INTEGER,
    birth_complications TEXT,
    delivery_type VARCHAR(50),
    current_weight_kg DECIMAL(4,3),
    current_length_cm DECIMAL(4,1),
    current_head_circumference_cm DECIMAL(4,1),
    blood_type VARCHAR(10),
    allergies TEXT,
    medical_conditions TEXT,
    medications TEXT,
    pediatrician_name VARCHAR(100),
    pediatrician_phone VARCHAR(20),
    hospital_of_birth VARCHAR(100),
    development_notes TEXT,
    feeding_type VARCHAR(50),
    sleep_pattern_notes TEXT,
    profile_picture_url TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Health records table (for tracking various health metrics)
CREATE TABLE health_records (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    mother_id BIGINT REFERENCES mothers(id) ON DELETE CASCADE,
    baby_id BIGINT REFERENCES babies(id) ON DELETE CASCADE,
    record_type health_record_type NOT NULL,
    record_date DATE NOT NULL,
    record_time TIME,
    
    -- Vitals data
    blood_pressure_systolic INTEGER,
    blood_pressure_diastolic INTEGER,
    heart_rate INTEGER,
    temperature DECIMAL(4,1),
    
    -- Weight and measurements
    weight_kg DECIMAL(5,2),
    height_cm DECIMAL(5,2),
    head_circumference_cm DECIMAL(4,1),
    
    -- Sleep data
    sleep_start_time TIME,
    sleep_end_time TIME,
    sleep_quality VARCHAR(20),
    sleep_duration_hours DECIMAL(3,1),
    
    -- General data
    notes TEXT,
    symptoms TEXT[],
    medications_taken TEXT[],
    
    -- Metadata
    recorded_by_user_id BIGINT REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Appointments table
CREATE TABLE appointments (
    id BIGSERIAL PRIMARY KEY,
    mother_id BIGINT REFERENCES mothers(id) ON DELETE CASCADE,
    baby_id BIGINT REFERENCES babies(id) ON DELETE CASCADE,
    doctor_user_id BIGINT REFERENCES users(id),
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    duration_minutes INTEGER DEFAULT 30,
    appointment_type VARCHAR(50),
    status appointment_status DEFAULT 'SCHEDULED',
    location VARCHAR(200),
    doctor_name VARCHAR(100),
    doctor_phone VARCHAR(20),
    purpose TEXT,
    notes TEXT,
    reminder_sent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Vaccinations table
CREATE TABLE vaccinations (
    id BIGSERIAL PRIMARY KEY,
    baby_id BIGINT REFERENCES babies(id) ON DELETE CASCADE,
    vaccine_name VARCHAR(100) NOT NULL,
    scheduled_date DATE,
    administered_date DATE,
    status vaccination_status DEFAULT 'SCHEDULED',
    dose_number INTEGER,
    batch_number VARCHAR(50),
    administered_by VARCHAR(100),
    location VARCHAR(100),
    side_effects TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Milestones table (for tracking baby development)
CREATE TABLE milestones (
    id BIGSERIAL PRIMARY KEY,
    baby_id BIGINT REFERENCES babies(id) ON DELETE CASCADE,
    milestone_name VARCHAR(100) NOT NULL,
    milestone_category VARCHAR(50), -- Physical, Cognitive, Social, Language
    expected_age_months INTEGER,
    achieved_date DATE,
    is_achieved BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Feeding records table
CREATE TABLE feeding_records (
    id BIGSERIAL PRIMARY KEY,
    baby_id BIGINT REFERENCES babies(id) ON DELETE CASCADE,
    feeding_date DATE NOT NULL,
    feeding_time TIME NOT NULL,
    feeding_type VARCHAR(20), -- Breast, Bottle, Solid
    amount_ml INTEGER,
    duration_minutes INTEGER,
    notes TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Growth tracking table
CREATE TABLE growth_records (
    id BIGSERIAL PRIMARY KEY,
    baby_id BIGINT REFERENCES babies(id) ON DELETE CASCADE,
    measurement_date DATE NOT NULL,
    weight_kg DECIMAL(4,3),
    length_cm DECIMAL(4,1),
    head_circumference_cm DECIMAL(4,1),
    weight_percentile DECIMAL(4,1),
    length_percentile DECIMAL(4,1),
    head_circumference_percentile DECIMAL(4,1),
    notes TEXT,
    measured_by VARCHAR(100),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Notifications table
CREATE TABLE notifications (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    notification_type VARCHAR(50), -- REMINDER, ALERT, INFO, WARNING
    is_read BOOLEAN DEFAULT FALSE,
    scheduled_for TIMESTAMP,
    sent_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_mothers_user_id ON mothers(user_id);
CREATE INDEX idx_babies_mother_id ON babies(mother_id);
CREATE INDEX idx_health_records_user_id ON health_records(user_id);
CREATE INDEX idx_health_records_date ON health_records(record_date);
CREATE INDEX idx_appointments_date ON appointments(appointment_date);
CREATE INDEX idx_vaccinations_baby_id ON vaccinations(baby_id);
CREATE INDEX idx_milestones_baby_id ON milestones(baby_id);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_mothers_updated_at BEFORE UPDATE ON mothers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_babies_updated_at BEFORE UPDATE ON babies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_health_records_updated_at BEFORE UPDATE ON health_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_appointments_updated_at BEFORE UPDATE ON appointments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_vaccinations_updated_at BEFORE UPDATE ON vaccinations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_milestones_updated_at BEFORE UPDATE ON milestones FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
