// Global JavaScript for Maternal & Child Health System

// Configuration
const CONFIG = {
    API_BASE_URL: 'http://localhost:8080/api',
    SUPABASE_URL: 'your-supabase-url',
    SUPABASE_ANON_KEY: 'your-supabase-anon-key'
};

// Utility Functions
class Utils {
    static formatDate(date) {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    static formatTime(time) {
        return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    static showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    static validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }

    static validatePhone(phone) {
        const re = /^\+?[\d\s\-\(\)]{10,}$/;
        return re.test(phone);
    }
}

// Authentication Manager
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.init();
    }

    init() {
        // Check for existing session
        const userData = localStorage.getItem('userData');
        if (userData) {
            this.currentUser = JSON.parse(userData);
        }
    }

    async login(email, password, role) {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, password, role })
            });

            if (response.ok) {
                const userData = await response.json();
                this.currentUser = userData;
                localStorage.setItem('userData', JSON.stringify(userData));
                this.redirectToDashboard(role);
                return { success: true };
            } else {
                const error = await response.json();
                return { success: false, message: error.message };
            }
        } catch (error) {
            return { success: false, message: 'Network error. Please try again.' };
        }
    }

    async register(userData) {
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}/auth/register`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(userData)
            });

            if (response.ok) {
                const result = await response.json();
                Utils.showNotification('Registration successful! Please login.', 'success');
                return { success: true };
            } else {
                const error = await response.json();
                return { success: false, message: error.message };
            }
        } catch (error) {
            return { success: false, message: 'Network error. Please try again.' };
        }
    }

    logout() {
        this.currentUser = null;
        localStorage.removeItem('userData');
        window.location.href = '/frontend/pages/index.html';
    }

    redirectToDashboard(role) {
        const dashboards = {
            'admin': '/frontend/pages/dashboard-admin.html',
            'doctor': '/frontend/pages/dashboard-doctor.html',
            'user': '/frontend/pages/dashboard-user.html'
        };
        
        window.location.href = dashboards[role] || dashboards['user'];
    }

    isAuthenticated() {
        return this.currentUser !== null;
    }

    getUser() {
        return this.currentUser;
    }
}

// API Manager
class APIManager {
    constructor() {
        this.authManager = new AuthManager();
    }

    async makeRequest(endpoint, options = {}) {
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json'
            }
        };

        if (this.authManager.isAuthenticated()) {
            defaultOptions.headers['Authorization'] = `Bearer ${this.authManager.getUser().token}`;
        }

        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const response = await fetch(`${CONFIG.API_BASE_URL}${endpoint}`, finalOptions);
            
            if (response.status === 401) {
                this.authManager.logout();
                return null;
            }
            
            return response;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    async get(endpoint) {
        const response = await this.makeRequest(endpoint);
        return response ? response.json() : null;
    }

    async post(endpoint, data) {
        const response = await this.makeRequest(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
        return response ? response.json() : null;
    }

    async put(endpoint, data) {
        const response = await this.makeRequest(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
        return response ? response.json() : null;
    }

    async delete(endpoint) {
        const response = await this.makeRequest(endpoint, {
            method: 'DELETE'
        });
        return response ? response.json() : null;
    }
}

// Form Handler
class FormHandler {
    static handleSubmit(formId, callback) {
        const form = document.getElementById(formId);
        if (form) {
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                const formData = new FormData(form);
                const data = Object.fromEntries(formData.entries());
                await callback(data);
            });
        }
    }

    static validateForm(formId, rules) {
        const form = document.getElementById(formId);
        if (!form) return false;

        let isValid = true;
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        for (const [field, rule] of Object.entries(rules)) {
            const value = data[field];
            const fieldElement = form.querySelector(`[name="${field}"]`);
            
            if (rule.required && (!value || value.trim() === '')) {
                this.showFieldError(fieldElement, `${rule.label} is required`);
                isValid = false;
            } else if (rule.type === 'email' && value && !Utils.validateEmail(value)) {
                this.showFieldError(fieldElement, 'Please enter a valid email address');
                isValid = false;
            } else if (rule.type === 'phone' && value && !Utils.validatePhone(value)) {
                this.showFieldError(fieldElement, 'Please enter a valid phone number');
                isValid = false;
            } else {
                this.clearFieldError(fieldElement);
            }
        }

        return isValid;
    }

    static showFieldError(field, message) {
        this.clearFieldError(field);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
        field.classList.add('error');
    }

    static clearFieldError(field) {
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        field.classList.remove('error');
    }
}

// Initialize global instances
const authManager = new AuthManager();
const apiManager = new APIManager();

// DOM Content Loaded Event
document.addEventListener('DOMContentLoaded', function() {
    // Add fade-in animation to main content
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }

    // Initialize navigation
    initializeNavigation();
    
    // Initialize page-specific functionality
    const currentPage = window.location.pathname.split('/').pop();
    initializePage(currentPage);
});

function initializeNavigation() {
    // Add logout functionality to logout buttons
    const logoutButtons = document.querySelectorAll('.logout-btn');
    logoutButtons.forEach(button => {
        button.addEventListener('click', (e) => {
            e.preventDefault();
            authManager.logout();
        });
    });
}

function initializePage(pageName) {
    switch(pageName) {
        case 'index.html':
            initializeHomePage();
            break;
        case 'login.html':
            initializeLoginPage();
            break;
        case 'dashboard-user.html':
            initializeUserDashboard();
            break;
        case 'dashboard-doctor.html':
            initializeDoctorDashboard();
            break;
        case 'dashboard-admin.html':
            initializeAdminDashboard();
            break;
    }
}

function initializeHomePage() {
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
}

function initializeLoginPage() {
    // Login form handling will be implemented in login.html
}

function initializeUserDashboard() {
    // User dashboard functionality will be implemented
}

function initializeDoctorDashboard() {
    // Doctor dashboard functionality will be implemented
}

function initializeAdminDashboard() {
    // Admin dashboard functionality will be implemented
}
