<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pregnancy Timeline - MaBa Health</title>
    <link rel="stylesheet" href="../../css/style.css">
    <style>
        .timeline-container {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .timeline {
            position: relative;
            padding: 2rem 0;
        }
        
        .timeline::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
            transform: translateX(-50%);
        }
        
        .timeline-item {
            position: relative;
            margin-bottom: 2rem;
            width: 50%;
        }
        
        .timeline-item:nth-child(odd) {
            left: 0;
            padding-right: 2rem;
        }
        
        .timeline-item:nth-child(even) {
            left: 50%;
            padding-left: 2rem;
        }
        
        .timeline-content {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            position: relative;
        }
        
        .timeline-item:nth-child(odd) .timeline-content::after {
            content: '';
            position: absolute;
            right: -10px;
            top: 20px;
            width: 0;
            height: 0;
            border: 10px solid transparent;
            border-left-color: white;
        }
        
        .timeline-item:nth-child(even) .timeline-content::after {
            content: '';
            position: absolute;
            left: -10px;
            top: 20px;
            width: 0;
            height: 0;
            border: 10px solid transparent;
            border-right-color: white;
        }
        
        .timeline-marker {
            position: absolute;
            top: 20px;
            width: 20px;
            height: 20px;
            background: #667eea;
            border: 4px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .timeline-item:nth-child(odd) .timeline-marker {
            right: -12px;
        }
        
        .timeline-item:nth-child(even) .timeline-marker {
            left: -12px;
        }
        
        .timeline-marker.completed {
            background: #28a745;
        }
        
        .timeline-marker.current {
            background: #ffc107;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
        }
        
        .week-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.5rem;
        }
        
        .trimester-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            background: #667eea;
            color: white;
            border-radius: 15px;
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }
        
        .baby-size {
            background: #f8f9ff;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            text-align: center;
        }
        
        .baby-size-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .current-week-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .week-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .stat-item {
            background: rgba(255,255,255,0.1);
            padding: 1rem;
            border-radius: 8px;
        }
        
        @media (max-width: 768px) {
            .timeline::before {
                left: 20px;
            }
            
            .timeline-item {
                width: 100%;
                left: 0 !important;
                padding-left: 3rem !important;
                padding-right: 0 !important;
            }
            
            .timeline-marker {
                left: 12px !important;
                right: auto !important;
            }
            
            .timeline-content::after {
                left: -10px !important;
                right: auto !important;
                border-right-color: white !important;
                border-left-color: transparent !important;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav container">
            <div class="logo">
                <img src="../../assets/images/logo.png" alt="Health Tracker Logo" onerror="this.style.display='none'">
                <span>MaBa Health</span>
            </div>
            <ul class="nav-links">
                <li><a href="../dashboard-user.html">← Back to Dashboard</a></li>
                <li><a href="#" class="logout-btn">Logout</a></li>
            </ul>
        </nav>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="dashboard">
                <div class="dashboard-header">
                    <h1>Pregnancy Timeline</h1>
                    <p>Track your pregnancy journey week by week</p>
                </div>

                <div class="current-week-card">
                    <h2>Week <span id="currentWeek">24</span></h2>
                    <p>You're in your <span id="currentTrimester">second</span> trimester</p>
                    <div class="week-stats">
                        <div class="stat-item">
                            <div style="font-size: 1.5rem; font-weight: bold;" id="daysToGo">112</div>
                            <div>Days to go</div>
                        </div>
                        <div class="stat-item">
                            <div style="font-size: 1.5rem; font-weight: bold;" id="babySize">Corn</div>
                            <div>Baby size</div>
                        </div>
                        <div class="stat-item">
                            <div style="font-size: 1.5rem; font-weight: bold;" id="dueDate">Aug 15</div>
                            <div>Due date</div>
                        </div>
                    </div>
                </div>

                <div class="timeline-container">
                    <div class="timeline" id="pregnancyTimeline">
                        <!-- Timeline items will be generated by JavaScript -->
                    </div>
                </div>

                <div class="card mt-2">
                    <h3>Pregnancy Milestones</h3>
                    <div class="grid grid-2">
                        <div>
                            <h4>✅ Completed</h4>
                            <ul id="completedMilestones">
                                <li>First prenatal appointment</li>
                                <li>First ultrasound</li>
                                <li>Genetic screening</li>
                                <li>Anatomy scan</li>
                            </ul>
                        </div>
                        <div>
                            <h4>📅 Upcoming</h4>
                            <ul id="upcomingMilestones">
                                <li>Glucose screening test</li>
                                <li>Third trimester ultrasound</li>
                                <li>Group B strep test</li>
                                <li>Birth plan discussion</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <h3>Weekly Development</h3>
                    <div id="weeklyDevelopment">
                        <h4>Week 24 Development</h4>
                        <p><strong>Baby's Development:</strong></p>
                        <ul>
                            <li>Baby is about 12 inches long and weighs about 1.3 pounds</li>
                            <li>Hearing is developing - baby can hear your voice!</li>
                            <li>Lungs are developing surfactant</li>
                            <li>Brain tissue is developing rapidly</li>
                        </ul>
                        
                        <p><strong>Your Body:</strong></p>
                        <ul>
                            <li>You may experience back pain as your belly grows</li>
                            <li>Possible swelling in hands and feet</li>
                            <li>Increased appetite</li>
                            <li>Possible stretch marks appearing</li>
                        </ul>
                        
                        <p><strong>Tips for This Week:</strong></p>
                        <ul>
                            <li>Start thinking about baby names</li>
                            <li>Consider taking childbirth classes</li>
                            <li>Stay hydrated and eat nutritious foods</li>
                            <li>Get plenty of rest</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="../../js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!authManager.isAuthenticated()) {
                window.location.href = '../login.html';
                return;
            }

            loadPregnancyData();
            generateTimeline();
        });

        async function loadPregnancyData() {
            try {
                const pregnancyData = await apiManager.get('/user/pregnancy-status');
                if (pregnancyData) {
                    document.getElementById('currentWeek').textContent = pregnancyData.week || '24';
                    document.getElementById('currentTrimester').textContent = getTrimeserName(pregnancyData.trimester || 2);
                    document.getElementById('daysToGo').textContent = pregnancyData.daysToGo || '112';
                    document.getElementById('babySize').textContent = pregnancyData.babySize || 'Corn';
                    document.getElementById('dueDate').textContent = pregnancyData.dueDate || 'Aug 15';
                }
            } catch (error) {
                console.error('Error loading pregnancy data:', error);
            }
        }

        function getTrimeserName(trimester) {
            const names = { 1: 'first', 2: 'second', 3: 'third' };
            return names[trimester] || 'second';
        }

        function generateTimeline() {
            const timeline = document.getElementById('pregnancyTimeline');
            const currentWeek = 24; // This would come from API
            
            const milestones = [
                { week: 4, title: 'Missed Period', description: 'First sign of pregnancy', icon: '🔍' },
                { week: 6, title: 'First Heartbeat', description: 'Baby\'s heart starts beating', icon: '💓' },
                { week: 8, title: 'First Prenatal Visit', description: 'Initial checkup with doctor', icon: '🏥' },
                { week: 12, title: 'End of First Trimester', description: 'Risk of miscarriage decreases', icon: '🎉' },
                { week: 16, title: 'Gender Reveal', description: 'Find out if it\'s a boy or girl', icon: '👶' },
                { week: 20, title: 'Anatomy Scan', description: 'Detailed ultrasound examination', icon: '📸' },
                { week: 24, title: 'Viability Milestone', description: 'Baby could survive outside womb', icon: '🌟' },
                { week: 28, title: 'Third Trimester', description: 'Final stretch begins', icon: '🏃‍♀️' },
                { week: 32, title: 'Baby Shower Time', description: 'Celebrate with friends and family', icon: '🎁' },
                { week: 36, title: 'Full Term Soon', description: 'Baby is almost ready', icon: '⏰' },
                { week: 40, title: 'Due Date', description: 'Baby\'s expected arrival', icon: '👶' }
            ];

            timeline.innerHTML = milestones.map((milestone, index) => {
                let markerClass = 'timeline-marker';
                if (milestone.week < currentWeek) {
                    markerClass += ' completed';
                } else if (milestone.week === currentWeek) {
                    markerClass += ' current';
                }

                return `
                    <div class="timeline-item">
                        <div class="timeline-content">
                            <div class="week-number">Week ${milestone.week}</div>
                            <div class="trimester-badge">${getTrimeserForWeek(milestone.week)} Trimester</div>
                            <h3>${milestone.icon} ${milestone.title}</h3>
                            <p>${milestone.description}</p>
                            ${milestone.week === currentWeek ? '<div class="baby-size"><div class="baby-size-icon">🌽</div><div>Size of a corn cob</div></div>' : ''}
                        </div>
                        <div class="${markerClass}"></div>
                    </div>
                `;
            }).join('');
        }

        function getTrimeserForWeek(week) {
            if (week <= 12) return 'First';
            if (week <= 27) return 'Second';
            return 'Third';
        }
    </script>
</body>
</html>
