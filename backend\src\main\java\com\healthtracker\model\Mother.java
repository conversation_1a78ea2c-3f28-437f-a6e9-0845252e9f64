package com.healthtracker.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Mother entity representing maternal health information
 */
@Entity
@Table(name = "mothers")
public class Mother {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @OneToOne
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    // Pregnancy Information
    @Column(name = "is_pregnant")
    private Boolean isPregnant = false;
    
    @Column(name = "due_date")
    private LocalDate dueDate;
    
    @Column(name = "last_menstrual_period")
    private LocalDate lastMenstrualPeriod;
    
    @Column(name = "pregnancy_week")
    private Integer pregnancyWeek;
    
    @Column(name = "pregnancy_trimester")
    private Integer pregnancyTrimester;
    
    @Column(name = "number_of_pregnancies")
    private Integer numberOfPregnancies = 0;
    
    @Column(name = "number_of_births")
    private Integer numberOfBirths = 0;
    
    @Column(name = "number_of_miscarriages")
    private Integer numberOfMiscarriages = 0;
    
    // Medical Information
    @Column(name = "blood_type")
    private String bloodType;
    
    @Column(name = "height_cm")
    private Double heightCm;
    
    @Column(name = "pre_pregnancy_weight_kg")
    private Double prePregnancyWeightKg;
    
    @Column(name = "current_weight_kg")
    private Double currentWeightKg;
    
    @Column(name = "bmi")
    private Double bmi;
    
    // Medical History
    @Column(name = "medical_conditions", columnDefinition = "TEXT")
    private String medicalConditions;
    
    @Column(name = "allergies", columnDefinition = "TEXT")
    private String allergies;
    
    @Column(name = "medications", columnDefinition = "TEXT")
    private String medications;
    
    @Column(name = "family_medical_history", columnDefinition = "TEXT")
    private String familyMedicalHistory;
    
    // Healthcare Provider
    @Column(name = "primary_doctor_name")
    private String primaryDoctorName;
    
    @Column(name = "primary_doctor_phone")
    private String primaryDoctorPhone;
    
    @Column(name = "hospital_preference")
    private String hospitalPreference;
    
    @Column(name = "insurance_provider")
    private String insuranceProvider;
    
    @Column(name = "insurance_policy_number")
    private String insurancePolicyNumber;
    
    // Preferences
    @Column(name = "preferred_language")
    private String preferredLanguage = "English";
    
    @Column(name = "notification_preferences", columnDefinition = "TEXT")
    private String notificationPreferences;
    
    @Column(name = "privacy_settings", columnDefinition = "TEXT")
    private String privacySettings;
    
    // Timestamps
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    // Note: Relationships will be added when Baby, HealthRecord, and Appointment entities are created
    
    // Constructors
    public Mother() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public Mother(User user) {
        this();
        this.user = user;
    }
    
    // Lifecycle callbacks
    @PreUpdate
    public void preUpdate() {
        this.updatedAt = LocalDateTime.now();
    }
    
    // Business methods
    public void updatePregnancyWeek() {
        if (isPregnant && lastMenstrualPeriod != null) {
            long daysSinceLMP = java.time.temporal.ChronoUnit.DAYS.between(lastMenstrualPeriod, LocalDate.now());
            this.pregnancyWeek = (int) (daysSinceLMP / 7);
            this.pregnancyTrimester = calculateTrimester(this.pregnancyWeek);
        }
    }
    
    private int calculateTrimester(int week) {
        if (week <= 12) return 1;
        else if (week <= 27) return 2;
        else return 3;
    }
    
    public void calculateBMI() {
        if (heightCm != null && currentWeightKg != null && heightCm > 0) {
            double heightM = heightCm / 100.0;
            this.bmi = currentWeightKg / (heightM * heightM);
        }
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }
    
    public Boolean getIsPregnant() { return isPregnant; }
    public void setIsPregnant(Boolean isPregnant) { this.isPregnant = isPregnant; }
    
    public LocalDate getDueDate() { return dueDate; }
    public void setDueDate(LocalDate dueDate) { this.dueDate = dueDate; }
    
    public LocalDate getLastMenstrualPeriod() { return lastMenstrualPeriod; }
    public void setLastMenstrualPeriod(LocalDate lastMenstrualPeriod) { 
        this.lastMenstrualPeriod = lastMenstrualPeriod;
        updatePregnancyWeek();
    }
    
    public Integer getPregnancyWeek() { return pregnancyWeek; }
    public void setPregnancyWeek(Integer pregnancyWeek) { this.pregnancyWeek = pregnancyWeek; }
    
    public Integer getPregnancyTrimester() { return pregnancyTrimester; }
    public void setPregnancyTrimester(Integer pregnancyTrimester) { this.pregnancyTrimester = pregnancyTrimester; }
    
    public Integer getNumberOfPregnancies() { return numberOfPregnancies; }
    public void setNumberOfPregnancies(Integer numberOfPregnancies) { this.numberOfPregnancies = numberOfPregnancies; }
    
    public Integer getNumberOfBirths() { return numberOfBirths; }
    public void setNumberOfBirths(Integer numberOfBirths) { this.numberOfBirths = numberOfBirths; }
    
    public Integer getNumberOfMiscarriages() { return numberOfMiscarriages; }
    public void setNumberOfMiscarriages(Integer numberOfMiscarriages) { this.numberOfMiscarriages = numberOfMiscarriages; }
    
    public String getBloodType() { return bloodType; }
    public void setBloodType(String bloodType) { this.bloodType = bloodType; }
    
    public Double getHeightCm() { return heightCm; }
    public void setHeightCm(Double heightCm) { 
        this.heightCm = heightCm;
        calculateBMI();
    }
    
    public Double getPrePregnancyWeightKg() { return prePregnancyWeightKg; }
    public void setPrePregnancyWeightKg(Double prePregnancyWeightKg) { this.prePregnancyWeightKg = prePregnancyWeightKg; }
    
    public Double getCurrentWeightKg() { return currentWeightKg; }
    public void setCurrentWeightKg(Double currentWeightKg) { 
        this.currentWeightKg = currentWeightKg;
        calculateBMI();
    }
    
    public Double getBmi() { return bmi; }
    public void setBmi(Double bmi) { this.bmi = bmi; }
    
    public String getMedicalConditions() { return medicalConditions; }
    public void setMedicalConditions(String medicalConditions) { this.medicalConditions = medicalConditions; }
    
    public String getAllergies() { return allergies; }
    public void setAllergies(String allergies) { this.allergies = allergies; }
    
    public String getMedications() { return medications; }
    public void setMedications(String medications) { this.medications = medications; }
    
    public String getFamilyMedicalHistory() { return familyMedicalHistory; }
    public void setFamilyMedicalHistory(String familyMedicalHistory) { this.familyMedicalHistory = familyMedicalHistory; }
    
    public String getPrimaryDoctorName() { return primaryDoctorName; }
    public void setPrimaryDoctorName(String primaryDoctorName) { this.primaryDoctorName = primaryDoctorName; }
    
    public String getPrimaryDoctorPhone() { return primaryDoctorPhone; }
    public void setPrimaryDoctorPhone(String primaryDoctorPhone) { this.primaryDoctorPhone = primaryDoctorPhone; }
    
    public String getHospitalPreference() { return hospitalPreference; }
    public void setHospitalPreference(String hospitalPreference) { this.hospitalPreference = hospitalPreference; }
    
    public String getInsuranceProvider() { return insuranceProvider; }
    public void setInsuranceProvider(String insuranceProvider) { this.insuranceProvider = insuranceProvider; }
    
    public String getInsurancePolicyNumber() { return insurancePolicyNumber; }
    public void setInsurancePolicyNumber(String insurancePolicyNumber) { this.insurancePolicyNumber = insurancePolicyNumber; }
    
    public String getPreferredLanguage() { return preferredLanguage; }
    public void setPreferredLanguage(String preferredLanguage) { this.preferredLanguage = preferredLanguage; }
    
    public String getNotificationPreferences() { return notificationPreferences; }
    public void setNotificationPreferences(String notificationPreferences) { this.notificationPreferences = notificationPreferences; }
    
    public String getPrivacySettings() { return privacySettings; }
    public void setPrivacySettings(String privacySettings) { this.privacySettings = privacySettings; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    // Relationship getters/setters will be added when related entities are created
}
