<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mother Dashboard - MaBa Health</title>
    <link rel="stylesheet" href="../css/style.css">
    <style>
        .dashboard-nav {
            background: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .dashboard-nav .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .action-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
            cursor: pointer;
        }
        
        .action-card:hover {
            transform: translateY(-3px);
        }
        
        .action-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .recent-activity {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f8f9ff;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
        }
    </style>
</head>
<body>
    <header class="dashboard-nav">
        <nav class="nav container">
            <div class="logo">
                <img src="../assets/images/logo.png" alt="Health Tracker Logo" onerror="this.style.display='none'">
                <span>MaBa Health</span>
            </div>
            <div class="user-info">
                <div class="user-avatar" id="userAvatar">M</div>
                <div>
                    <div id="userName">Welcome, Mother</div>
                    <small style="color: #666;">Mother Dashboard</small>
                </div>
                <button class="btn logout-btn">Logout</button>
            </div>
        </nav>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="dashboard">
                <div class="dashboard-header">
                    <h1>Your Health Dashboard</h1>
                    <div class="dashboard-stats">
                        <div class="stat-card">
                            <div class="stat-number" id="pregnancyWeek">24</div>
                            <div>Weeks Pregnant</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="nextAppointment">3</div>
                            <div>Days to Next Appointment</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number" id="healthScore">85</div>
                            <div>Health Score</div>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <div class="action-card" onclick="navigateTo('mother/health-tracker.html')">
                        <div class="action-icon">📊</div>
                        <h3>Health Tracker</h3>
                        <p>Monitor your daily health metrics</p>
                    </div>
                    
                    <div class="action-card" onclick="navigateTo('mother/pregnancy-timeline.html')">
                        <div class="action-icon">📅</div>
                        <h3>Pregnancy Timeline</h3>
                        <p>Track your pregnancy journey</p>
                    </div>
                    
                    <div class="action-card" onclick="navigateTo('mother/doctor-appointments.html')">
                        <div class="action-icon">🏥</div>
                        <h3>Appointments</h3>
                        <p>Schedule and manage appointments</p>
                    </div>
                    
                    <div class="action-card" onclick="navigateTo('mother/nutrition-chart.html')">
                        <div class="action-icon">🥗</div>
                        <h3>Nutrition</h3>
                        <p>Plan healthy meals</p>
                    </div>
                    
                    <div class="action-card" onclick="navigateTo('mother/meds-reminder.html')">
                        <div class="action-icon">💊</div>
                        <h3>Medications</h3>
                        <p>Track medication schedule</p>
                    </div>
                    
                    <div class="action-card" onclick="navigateTo('mother/meditation.html')">
                        <div class="action-icon">🧘‍♀️</div>
                        <h3>Wellness</h3>
                        <p>Meditation and relaxation</p>
                    </div>
                    
                    <div class="action-card" onclick="navigateTo('mother/chat-doctor.html')">
                        <div class="action-icon">💬</div>
                        <h3>Chat with Doctor</h3>
                        <p>Communicate with your healthcare provider</p>
                    </div>
                    
                    <div class="action-card" onclick="navigateTo('mother/danger-signs.html')">
                        <div class="action-icon">⚠️</div>
                        <h3>Danger Signs</h3>
                        <p>Important warning signs to watch</p>
                    </div>
                </div>

                <div class="grid">
                    <div class="recent-activity">
                        <h3>Recent Activity</h3>
                        <div class="activity-item">
                            <div class="activity-icon">📊</div>
                            <div>
                                <div>Health metrics updated</div>
                                <small style="color: #666;">2 hours ago</small>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">💊</div>
                            <div>
                                <div>Prenatal vitamin taken</div>
                                <small style="color: #666;">8 hours ago</small>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">🏥</div>
                            <div>
                                <div>Appointment scheduled</div>
                                <small style="color: #666;">1 day ago</small>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <h3>Quick Links</h3>
                        <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                            <a href="mother/personal-info.html" class="btn" style="text-align: center;">Personal Information</a>
                            <a href="mother/vaccination-prep.html" class="btn" style="text-align: center;">Vaccination Prep</a>
                            <a href="mother/health-reports-upload.html" class="btn" style="text-align: center;">Upload Reports</a>
                            <a href="mother/daily-checklist.html" class="btn" style="text-align: center;">Daily Checklist</a>
                            <a href="mother/educational-content.html" class="btn" style="text-align: center;">Educational Content</a>
                            <a href="mother/emergency-contact.html" class="btn btn-secondary" style="text-align: center;">Emergency Contacts</a>
                        </div>
                    </div>
                </div>

                <div class="card mt-2">
                    <h3>Child Tracking</h3>
                    <p>Ready to track your child's health and development?</p>
                    <div class="quick-actions">
                        <div class="action-card" onclick="navigateTo('baby/baby-profile.html')">
                            <div class="action-icon">👶</div>
                            <h4>Baby Profile</h4>
                        </div>
                        <div class="action-card" onclick="navigateTo('baby/vaccination-schedule.html')">
                            <div class="action-icon">💉</div>
                            <h4>Vaccinations</h4>
                        </div>
                        <div class="action-card" onclick="navigateTo('baby/growth-tracker.html')">
                            <div class="action-icon">📏</div>
                            <h4>Growth Tracker</h4>
                        </div>
                        <div class="action-card" onclick="navigateTo('baby/milestone-tracker.html')">
                            <div class="action-icon">🎯</div>
                            <h4>Milestones</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="../js/script.js"></script>
    <script>
        function navigateTo(page) {
            window.location.href = page;
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!authManager.isAuthenticated()) {
                window.location.href = 'login.html';
                return;
            }

            const user = authManager.getUser();
            if (user) {
                document.getElementById('userName').textContent = `Welcome, ${user.name || 'Mother'}`;
                document.getElementById('userAvatar').textContent = (user.name || 'M').charAt(0).toUpperCase();
            }

            // Load dashboard data
            loadDashboardData();
        });

        async function loadDashboardData() {
            try {
                // Load pregnancy data
                const pregnancyData = await apiManager.get('/user/pregnancy-status');
                if (pregnancyData) {
                    document.getElementById('pregnancyWeek').textContent = pregnancyData.week || '24';
                }

                // Load next appointment
                const appointments = await apiManager.get('/user/appointments/next');
                if (appointments) {
                    const daysToNext = Math.ceil((new Date(appointments.date) - new Date()) / (1000 * 60 * 60 * 24));
                    document.getElementById('nextAppointment').textContent = daysToNext > 0 ? daysToNext : '0';
                }

                // Load health score
                const healthData = await apiManager.get('/user/health-score');
                if (healthData) {
                    document.getElementById('healthScore').textContent = healthData.score || '85';
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }
    </script>
</body>
</html>
