# Maternal & Child Health Tracking System

A comprehensive web application for tracking maternal and child health, built with HTML/CSS/JavaScript frontend and Java Spring Boot backend, using Supabase for database and authentication.

## Features

### For Mothers (MotherTracker)
- Personal information management
- Pregnancy timeline tracking
- Health metrics monitoring
- Doctor appointment scheduling
- Nutrition chart and meal planning
- Medication reminders
- Meditation and wellness content
- Doctor chat functionality
- Danger signs awareness
- Vaccination preparation
- Health reports upload
- Daily health checklist
- Educational content
- Emergency contacts

### For Children (ChildTracker)
- Baby profile management
- Vaccination schedule tracking
- Growth and development monitoring
- Milestone tracking
- Feeding schedule management
- Sleep pattern tracking
- Health checkup records
- AI-powered health alerts
- Unique child ID system
- Common illness information
- AI chatbot assistance
- Baby photo gallery

### For Healthcare Providers
- Patient management dashboard
- Health data analytics
- Appointment management
- Medical record access
- Communication tools

### For Administrators
- User management
- System analytics
- Content management
- Security oversight

## Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Backend**: Java Spring Boot
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Storage**: Supabase Storage
- **Deployment**: Web-based application

## Project Structure

```
maternal-child-health-system/
├── frontend/           # Frontend web application
├── backend/           # Java Spring Boot API
├── supabase/          # Database schema and configuration
└── docs/              # Documentation
```

## Getting Started

### Prerequisites
- Java 11 or higher
- Node.js (for development tools)
- Supabase account

### Installation

1. Clone the repository
2. Set up Supabase database using provided schema
3. Configure backend application properties
4. Run the Spring Boot application
5. Serve the frontend files

## License

This project is licensed under the MIT License - see the LICENSE file for details.
