<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baby Profile - MaBa Health</title>
    <link rel="stylesheet" href="../../css/style.css">
    <style>
        .baby-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .baby-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid white;
            margin: 0 auto 1rem;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .baby-avatar:hover {
            transform: scale(1.05);
        }
        
        .baby-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .info-item {
            background: rgba(255,255,255,0.1);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }
        
        .info-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }
        
        .info-label {
            font-size: 0.875rem;
            opacity: 0.9;
        }
        
        .growth-chart {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .chart-container {
            height: 300px;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            margin: 1rem 0;
        }
        
        .milestone-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            transition: background-color 0.3s ease;
        }
        
        .milestone-item:hover {
            background-color: #f8f9ff;
        }
        
        .milestone-item:last-child {
            border-bottom: none;
        }
        
        .milestone-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 1.5rem;
        }
        
        .milestone-achieved {
            background: #d4edda;
            color: #155724;
        }
        
        .milestone-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .milestone-overdue {
            background: #f8d7da;
            color: #721c24;
        }
        
        .milestone-content {
            flex: 1;
        }
        
        .milestone-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        
        .milestone-age {
            font-size: 0.875rem;
            color: #666;
        }
        
        .photo-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .photo-item {
            aspect-ratio: 1;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .photo-item:hover {
            transform: scale(1.05);
        }
        
        .photo-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .add-photo {
            border: 2px dashed #667eea;
            color: #667eea;
            font-size: 2rem;
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .stat-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .stat-value {
            font-size: 1.25rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: #666;
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav container">
            <div class="logo">
                <img src="../../assets/images/logo.png" alt="Health Tracker Logo" onerror="this.style.display='none'">
                <span>MaBa Health</span>
            </div>
            <ul class="nav-links">
                <li><a href="../dashboard-user.html">← Back to Dashboard</a></li>
                <li><a href="#" class="logout-btn">Logout</a></li>
            </ul>
        </nav>
    </header>

    <main class="main-content">
        <div class="container">
            <div class="baby-header">
                <div class="baby-avatar" onclick="uploadPhoto()">
                    <span id="babyInitial">👶</span>
                </div>
                <h1 id="babyName">Baby Emma</h1>
                <p id="babyAge">8 months old</p>
                <div class="baby-info-grid">
                    <div class="info-item">
                        <div class="info-value" id="babyWeight">8.2 kg</div>
                        <div class="info-label">Current Weight</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value" id="babyHeight">68 cm</div>
                        <div class="info-label">Current Height</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value" id="babyId">BABY001</div>
                        <div class="info-label">Unique ID</div>
                    </div>
                    <div class="info-item">
                        <div class="info-value" id="nextCheckup">5 days</div>
                        <div class="info-label">Next Checkup</div>
                    </div>
                </div>
            </div>

            <div class="quick-stats">
                <div class="stat-card">
                    <div class="stat-icon">💉</div>
                    <div class="stat-value" id="vaccinesCompleted">12</div>
                    <div class="stat-label">Vaccines Completed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🎯</div>
                    <div class="stat-value" id="milestonesAchieved">15</div>
                    <div class="stat-label">Milestones Achieved</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">📏</div>
                    <div class="stat-value" id="growthPercentile">75th</div>
                    <div class="stat-label">Growth Percentile</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">🍼</div>
                    <div class="stat-value" id="feedingType">Mixed</div>
                    <div class="stat-label">Feeding Type</div>
                </div>
            </div>

            <div class="grid">
                <div class="growth-chart">
                    <h3>Growth Chart</h3>
                    <div class="chart-container">
                        Growth Chart Visualization
                        <br><small>(Interactive chart would be implemented here)</small>
                    </div>
                    <div class="text-center">
                        <button class="btn" onclick="updateMeasurements()">Update Measurements</button>
                    </div>
                </div>

                <div class="card">
                    <h3>Recent Milestones</h3>
                    <div id="milestonesList">
                        <div class="milestone-item">
                            <div class="milestone-icon milestone-achieved">✅</div>
                            <div class="milestone-content">
                                <div class="milestone-title">Sitting without support</div>
                                <div class="milestone-age">Achieved at 7 months</div>
                            </div>
                        </div>
                        <div class="milestone-item">
                            <div class="milestone-icon milestone-achieved">✅</div>
                            <div class="milestone-content">
                                <div class="milestone-title">Crawling</div>
                                <div class="milestone-age">Achieved at 8 months</div>
                            </div>
                        </div>
                        <div class="milestone-item">
                            <div class="milestone-icon milestone-pending">⏳</div>
                            <div class="milestone-content">
                                <div class="milestone-title">Standing with support</div>
                                <div class="milestone-age">Expected at 9 months</div>
                            </div>
                        </div>
                        <div class="milestone-item">
                            <div class="milestone-icon milestone-pending">⏳</div>
                            <div class="milestone-content">
                                <div class="milestone-title">First words</div>
                                <div class="milestone-age">Expected at 10-12 months</div>
                            </div>
                        </div>
                    </div>
                    <div class="text-center mt-1">
                        <a href="milestone-tracker.html" class="btn">View All Milestones</a>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>Baby Photos</h3>
                <div class="photo-gallery">
                    <div class="photo-item add-photo" onclick="uploadPhoto()">
                        <span>+</span>
                    </div>
                    <div class="photo-item">
                        <div style="background: #f0f0f0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">📸</div>
                    </div>
                    <div class="photo-item">
                        <div style="background: #f0f0f0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">📸</div>
                    </div>
                    <div class="photo-item">
                        <div style="background: #f0f0f0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;">📸</div>
                    </div>
                </div>
                <div class="text-center mt-1">
                    <a href="baby-images.html" class="btn">View All Photos</a>
                </div>
            </div>

            <div class="grid">
                <div class="card">
                    <h3>Quick Actions</h3>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <a href="vaccination-schedule.html" class="btn">💉 Vaccination Schedule</a>
                        <a href="growth-tracker.html" class="btn">📏 Growth Tracker</a>
                        <a href="feeding-tracker.html" class="btn">🍼 Feeding Tracker</a>
                        <a href="sleep-schedule.html" class="btn">😴 Sleep Schedule</a>
                        <a href="health-checkups.html" class="btn">🏥 Health Checkups</a>
                    </div>
                </div>

                <div class="card">
                    <h3>Baby Information</h3>
                    <form id="babyInfoForm">
                        <div class="form-group">
                            <label>Baby Name</label>
                            <input type="text" name="name" class="form-control" value="Emma">
                        </div>
                        <div class="form-group">
                            <label>Date of Birth</label>
                            <input type="date" name="dateOfBirth" class="form-control" value="2023-06-15">
                        </div>
                        <div class="form-group">
                            <label>Gender</label>
                            <select name="gender" class="form-control">
                                <option value="FEMALE" selected>Female</option>
                                <option value="MALE">Male</option>
                                <option value="OTHER">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Blood Type</label>
                            <input type="text" name="bloodType" class="form-control" value="O+">
                        </div>
                        <div class="form-group">
                            <label>Pediatrician</label>
                            <input type="text" name="pediatrician" class="form-control" value="Dr. Sarah Johnson">
                        </div>
                        <button type="submit" class="btn">Update Information</button>
                    </form>
                </div>
            </div>
        </div>
    </main>

    <script src="../../js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            if (!authManager.isAuthenticated()) {
                window.location.href = '../login.html';
                return;
            }

            loadBabyProfile();
            initializeForms();
        });

        async function loadBabyProfile() {
            try {
                const babyData = await apiManager.get('/user/babies/current');
                if (babyData) {
                    updateBabyProfile(babyData);
                }
            } catch (error) {
                console.error('Error loading baby profile:', error);
            }
        }

        function updateBabyProfile(baby) {
            document.getElementById('babyName').textContent = baby.name || 'Baby Emma';
            document.getElementById('babyAge').textContent = baby.ageString || '8 months old';
            document.getElementById('babyWeight').textContent = baby.currentWeightKg ? `${baby.currentWeightKg} kg` : '8.2 kg';
            document.getElementById('babyHeight').textContent = baby.currentLengthCm ? `${baby.currentLengthCm} cm` : '68 cm';
            document.getElementById('babyId').textContent = baby.uniqueId || 'BABY001';
            
            if (baby.name) {
                document.getElementById('babyInitial').textContent = baby.name.charAt(0).toUpperCase();
            }
        }

        function initializeForms() {
            FormHandler.handleSubmit('babyInfoForm', async (data) => {
                const result = await apiManager.put('/user/babies/current', data);
                if (result) {
                    Utils.showNotification('Baby information updated successfully!', 'success');
                    loadBabyProfile();
                }
            });
        }

        function uploadPhoto() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (file) {
                    try {
                        // Upload photo logic would go here
                        Utils.showNotification('Photo uploaded successfully!', 'success');
                    } catch (error) {
                        Utils.showNotification('Error uploading photo', 'error');
                    }
                }
            };
            input.click();
        }

        function updateMeasurements() {
            // This would open a modal or navigate to measurement update page
            window.location.href = 'growth-tracker.html';
        }
    </script>
</body>
</html>
